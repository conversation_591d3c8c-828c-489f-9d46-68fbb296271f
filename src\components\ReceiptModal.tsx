import React, { useRef } from 'react';
import { PrinterIcon, MailIcon } from 'lucide-react';
import { OrderItem, CustomerInfo, OrderType } from '../types';
import { formatPrice } from '../services/itemService';
import { useCurrency } from '../contexts/CurrencyContext';

interface ReceiptModalProps {
  order: OrderItem[];
  customerInfo?: CustomerInfo;
  orderType?: OrderType;
  discount?: number;
  discountType?: 'percentage' | 'fixed';
  onClose: () => void;
}

export function ReceiptModal({
  order,
  customerInfo,
  orderType = 'dine-in',
  discount = 0,
  discountType = 'percentage',
  onClose
}: ReceiptModalProps) {
  const receiptRef = useRef<HTMLDivElement>(null);
  const { defaultCurrency } = useCurrency();

  const calculateSubtotal = () => {
    return order.reduce((total, item) => {
      const modifierPrice = item.modifierPrice || 0;
      return total + ((item.price || 0) + modifierPrice) * item.quantity;
    }, 0);
  };

  const calculateDiscount = () => {
    const subtotal = calculateSubtotal();
    if (discountType === 'percentage') {
      return subtotal * (discount / 100);
    }
    return Math.min(discount, subtotal);
  };

  const calculateTaxableAmount = () => {
    return calculateSubtotal() - calculateDiscount();
  };

  const calculateTax = () => {
    return calculateTaxableAmount() * 0.0825;
  };

  const calculateTotal = () => {
    return calculateTaxableAmount() + calculateTax();
  };

  const handlePrint = () => {
    if (receiptRef.current) {
      const printContent = receiptRef.current.innerHTML;
      const originalContent = document.body.innerHTML;
      document.body.innerHTML = printContent;
      window.print();
      document.body.innerHTML = originalContent;
      window.location.reload();
    }
  };

  const handleEmail = () => {
    alert('Email functionality would be implemented here');
  };

  const formatDate = (date: Date) => {
    return new Intl.DateTimeFormat('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    }).format(date);
  };

  const orderTypeLabels = {
    'dine-in': 'Dine In',
    'takeout': 'Takeout',
    'delivery': 'Delivery'
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg w-full max-w-md p-6 max-h-[90vh] overflow-y-auto">
        <div className="flex justify-between items-center mb-4">
          <h2 className="text-xl font-bold">Receipt</h2>
          <div className="flex space-x-2">
            <button
              onClick={handlePrint}
              className="p-2 text-blue-600 hover:text-blue-800 transition-colors"
              title="Print Receipt"
            >
              <PrinterIcon size={20} />
            </button>
            <button
              onClick={handleEmail}
              className="p-2 text-blue-600 hover:text-blue-800 transition-colors"
              title="Email Receipt"
            >
              <MailIcon size={20} />
            </button>
            <button
              onClick={onClose}
              className="text-gray-500 hover:text-gray-700 text-xl"
            >
              ✕
            </button>
          </div>
        </div>

        <div ref={receiptRef} className="border p-4 mb-4 bg-white">
          {/* Header */}
          <div className="text-center mb-4">
            <h3 className="font-bold text-lg">POS System</h3>
            <p className="text-sm text-gray-600">123 Main Street</p>
            <p className="text-sm text-gray-600">Phone: (*************</p>
            <p className="text-sm text-gray-600">
              Receipt #{Math.floor(Math.random() * 10000)}
            </p>
            <p className="text-sm text-gray-600">{formatDate(new Date())}</p>
          </div>

          {/* Order Info */}
          <div className="mb-4 pb-2 border-b">
            <div className="flex justify-between text-sm">
              <span className="font-medium">Order Type:</span>
              <span>{orderTypeLabels[orderType]}</span>
            </div>
            {customerInfo?.name && (
              <div className="flex justify-between text-sm">
                <span className="font-medium">Customer:</span>
                <span>{customerInfo.name}</span>
              </div>
            )}
            {customerInfo?.phone && (
              <div className="flex justify-between text-sm">
                <span className="font-medium">Phone:</span>
                <span>{customerInfo.phone}</span>
              </div>
            )}
          </div>

          {/* Items */}
          <div className="border-t border-b py-2 mb-2">
            <div className="flex justify-between text-sm font-medium mb-1">
              <span className="w-1/2">Item</span>
              <span className="w-1/6 text-center">Qty</span>
              <span className="w-1/3 text-right">Amount</span>
            </div>
            {order.map((item, index) => (
              <div key={index} className="text-sm mb-2">
                <div className="flex justify-between">
                  <span className="w-1/2">{item.name}</span>
                  <span className="w-1/6 text-center">{item.quantity}</span>
                  <span className="w-1/3 text-right">
                    {formatPrice(((item.price || 0) + (item.modifierPrice || 0)) * item.quantity, defaultCurrency)}
                  </span>
                </div>
                {item.modifiers && item.modifiers.length > 0 && (
                  <div>
                    {item.modifiers.map(mod => (
                      <div key={mod.id} className="flex justify-between pl-4 text-xs text-gray-600">
                        <span className="w-1/2">+ {mod.name}</span>
                        <span className="w-1/6 text-center">{item.quantity}</span>
                        <span className="w-1/3 text-right">
                          {formatPrice(mod.price * item.quantity, defaultCurrency)}
                        </span>
                      </div>
                    ))}
                  </div>
                )}
              </div>
            ))}
          </div>

          {/* Totals */}
          <div className="text-sm space-y-1">
            <div className="flex justify-between">
              <span>Subtotal</span>
              <span>{formatPrice(calculateSubtotal(), defaultCurrency)}</span>
            </div>
            {discount > 0 && (
              <div className="flex justify-between text-green-600">
                <span>
                  Discount ({discountType === 'percentage' ? `${discount}%` : formatPrice(discount, defaultCurrency)})
                </span>
                <span>-{formatPrice(calculateDiscount(), defaultCurrency)}</span>
              </div>
            )}
            <div className="flex justify-between">
              <span>Taxable Amount</span>
              <span>{formatPrice(calculateTaxableAmount(), defaultCurrency)}</span>
            </div>
            <div className="flex justify-between">
              <span>Tax (8.25%)</span>
              <span>{formatPrice(calculateTax(), defaultCurrency)}</span>
            </div>
            <div className="flex justify-between font-bold text-base border-t pt-1">
              <span>Total</span>
              <span>{formatPrice(calculateTotal(), defaultCurrency)}</span>
            </div>
          </div>

          <div className="mt-4 text-center text-sm text-gray-600">
            <p>Thank you for your purchase!</p>
            <p>Please come again!</p>
          </div>
        </div>

        <div className="flex justify-end">
          <button
            onClick={onClose}
            className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
          >
            New Order
          </button>
        </div>
      </div>
    </div>
  );
}