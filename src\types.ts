export interface Modifier {
  id: string;
  name: string;
  price: number;
}

export interface Product {
  id: number;
  familyId: number;
  name: string;
  abbreviation: string;
  stmp: number;
  image: string;
  isDeleted: boolean;
  deactivated: boolean;
  notSubjectToDiscount: boolean;
  discount: number;
  cosmeticTax: number;
  vat: number;
  tax1Amount: number;
  tax2Percentage: number;
  isExpiry: boolean;
  // Additional properties for UI compatibility
  familyName?: string;
  price?: number;
  category?: string;
  hasModifiers?: boolean;
  modifiers?: Modifier[];
}

export interface Family {
  id: number;
  name: string;
  parentId: number;
  stmp: number;
  isDeleted: boolean;
}

export interface ItemUnit {
  id: number;
  itemId: number;
  unitId: number;
  symbol: string;
  barcode: string;
  isBasicUnit: boolean;
  isDefaultUnit: boolean;
  stmp: number;
  isDeleted: boolean;
  formula: number;
  image: string | null;
}

export interface UnitPrice {
  id: number;
  itemUnitId: number;
  price: number;
  price2: number;
  stmp: number;
  priceListId: number;
}

export interface Currency {
  id: number;
  name: string;
  nameAr: string | null;
  nameFr: string | null;
  symbol: string;
  isDeleted: boolean;
  stmp: number;
}

export interface Option {
  id: number;
  description: string;
  value: string;
}

export interface Category {
  id: string;
  name: string;
}

export interface OrderItem extends Product {
  quantity: number;
  modifiers?: Modifier[];
  modifierPrice?: number;
}

export interface CustomerInfo {
  name: string;
  phone: string;
}

export interface OrderSummaryData {
  items: OrderItem[];
  subtotal: number;
  tax: number;
  discount: number;
  total: number;
  orderType: 'dine-in' | 'takeout' | 'delivery';
  customer?: CustomerInfo;
}

export type OrderType = 'dine-in' | 'takeout' | 'delivery';

export interface Invoice {
  id: string;
  invoiceNumber: string;
  orderNumber: string;
  date: string;
  time: string;
  orderType: OrderType;
  customerInfo: CustomerInfo;
  items: OrderItem[];
  subtotal: number;
  discount: number;
  discountType: 'percentage' | 'fixed';
  taxableAmount: number;
  tax: number;
  taxRate: number;
  total: number;
  paymentMethod?: string;
  paymentStatus: 'pending' | 'paid' | 'refunded';
  status: 'pending' | 'preparing' | 'ready' | 'completed' | 'cancelled';
  createdBy: string;
  createdAt: number;
  updatedAt: number;
}

export interface Receipt {
  id: string;
  invoiceId: string;
  receiptNumber: string;
  date: string;
  time: string;
  customerInfo: CustomerInfo;
  items: OrderItem[];
  subtotal: number;
  discount: number;
  discountType: 'percentage' | 'fixed';
  tax: number;
  total: number;
  paymentMethod: string;
  createdAt: number;
}

export interface Order {
  id: string;
  orderNumber: string;
  invoiceId: string;
  date: string;
  time: string;
  orderType: OrderType;
  customerInfo: CustomerInfo;
  items: OrderItem[];
  total: number;
  status: 'pending' | 'preparing' | 'ready' | 'completed' | 'cancelled';
  createdBy: string;
  createdAt: number;
  updatedAt: number;
  tableId?: number; // For dine-in orders
  tableName?: string; // Table display name
}

// Rename to avoid conflict with layout tables
export interface DiningTable {
  id: string; // Composite key: `${layoutId}-${tableId}`
  tableId: number; // Original table ID from layout
  number: number;
  seats: number;
  status: 'available' | 'occupied' | 'reserved';
  currentOrderId?: string; // Link to active order
  lastOrderTime?: number; // Last order timestamp
  layoutId: string; // Reference to which layout this table belongs to
  name: string; // Display name for table
}

// Layout table for floor plan management
export interface LayoutTable {
  id: number;
  x: number;
  y: number;
  width: number;
  height: number;
  number: number;
  seats: number;
  status: 'available' | 'occupied' | 'reserved';
  borderColor: string;
}

// Layout for floor plan management
export interface TableLayout {
  id: string;
  name: string;
  tables: LayoutTable[];
  floorPlanImage: string | null;
  isActive: boolean; // Which layout is currently active
  createdAt: string;
  updatedAt: string;
}