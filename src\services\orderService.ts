import { dbService } from './indexedDBService';
import { appUser } from './authService';
import { Order, Invoice, Receipt, OrderItem, CustomerInfo, OrderType } from '../types';
import { linkOrderToTable, clearTable, parseDiningTableId } from './tableService';

// Generate unique IDs
function generateId(): string {
  return Date.now().toString(36) + Math.random().toString(36).substr(2);
}

// Generate order number (format: ORD-YYYYMMDD-XXXX)
function generateOrderNumber(): string {
  const date = new Date();
  const dateStr = date.getFullYear().toString() + 
                  (date.getMonth() + 1).toString().padStart(2, '0') + 
                  date.getDate().toString().padStart(2, '0');
  const sequence = Math.floor(Math.random() * 9999).toString().padStart(4, '0');
  return `ORD-${dateStr}-${sequence}`;
}

// Generate invoice number (format: INV-YYYYMMDD-XXXX)
function generateInvoiceNumber(): string {
  const date = new Date();
  const dateStr = date.getFullYear().toString() + 
                  (date.getMonth() + 1).toString().padStart(2, '0') + 
                  date.getDate().toString().padStart(2, '0');
  const sequence = Math.floor(Math.random() * 9999).toString().padStart(4, '0');
  return `INV-${dateStr}-${sequence}`;
}

// Generate receipt number (format: RCP-YYYYMMDD-XXXX)
function generateReceiptNumber(): string {
  const date = new Date();
  const dateStr = date.getFullYear().toString() + 
                  (date.getMonth() + 1).toString().padStart(2, '0') + 
                  date.getDate().toString().padStart(2, '0');
  const sequence = Math.floor(Math.random() * 9999).toString().padStart(4, '0');
  return `RCP-${dateStr}-${sequence}`;
}

// Calculate order totals
export function calculateOrderTotals(
  items: OrderItem[], 
  discount: number = 0, 
  discountType: 'percentage' | 'fixed' = 'percentage',
  taxRate: number = 0.0825
) {
  const subtotal = items.reduce((total, item) => {
    const modifierPrice = item.modifierPrice || 0;
    return total + (item.price || 0 + modifierPrice) * item.quantity;
  }, 0);

  const discountAmount = discountType === 'percentage' 
    ? subtotal * (discount / 100)
    : Math.min(discount, subtotal);

  const taxableAmount = subtotal - discountAmount;
  const tax = taxableAmount * taxRate;
  const total = taxableAmount + tax;

  return {
    subtotal,
    discount: discountAmount,
    taxableAmount,
    tax,
    total
  };
}

// Checkout function - saves order, invoice, and receipt
export async function checkout(
  items: OrderItem[],
  customerInfo: CustomerInfo,
  orderType: OrderType,
  discount: number = 0,
  discountType: 'percentage' | 'fixed' = 'percentage',
  paymentMethod: string = 'cash',
  tableId?: string // Now accepts composite ID for dine-in orders
): Promise<{ order: Order; invoice: Invoice; receipt: Receipt }> {
  
  if (!items || items.length === 0) {
    throw new Error('Cannot checkout with empty order');
  }

  await dbService.init();

  const now = Date.now();
  const date = new Date(now);
  const dateStr = date.toLocaleDateString();
  const timeStr = date.toLocaleTimeString();

  // Calculate totals
  const totals = calculateOrderTotals(items, discount, discountType);

  // Generate IDs and numbers
  const orderId = generateId();
  const invoiceId = generateId();
  const receiptId = generateId();
  const orderNumber = generateOrderNumber();
  const invoiceNumber = generateInvoiceNumber();
  const receiptNumber = generateReceiptNumber();

  // Create order
  let originalTableId: number | undefined;
  let tableName: string | undefined;

  if (orderType === 'dine-in' && tableId) {
    const { tableId: parsedTableId } = parseDiningTableId(tableId);
    originalTableId = parsedTableId;
    tableName = `Table ${parsedTableId}`;
  }

  const order: Order = {
    id: orderId,
    orderNumber,
    invoiceId,
    date: dateStr,
    time: timeStr,
    orderType,
    customerInfo,
    items: [...items], // Deep copy
    total: totals.total,
    status: 'pending',
    createdBy: appUser.UserName || 'Unknown',
    createdAt: now,
    updatedAt: now,
    tableId: originalTableId,
    tableName
  };

  // Create invoice
  const invoice: Invoice = {
    id: invoiceId,
    invoiceNumber,
    orderNumber,
    date: dateStr,
    time: timeStr,
    orderType,
    customerInfo,
    items: [...items], // Deep copy
    subtotal: totals.subtotal,
    discount: totals.discount,
    discountType,
    taxableAmount: totals.taxableAmount,
    tax: totals.tax,
    taxRate: 0.0825,
    total: totals.total,
    paymentMethod,
    paymentStatus: 'paid',
    status: 'pending',
    createdBy: appUser.UserName || 'Unknown',
    createdAt: now,
    updatedAt: now
  };

  // Create receipt
  const receipt: Receipt = {
    id: receiptId,
    invoiceId,
    receiptNumber,
    date: dateStr,
    time: timeStr,
    customerInfo,
    items: [...items], // Deep copy
    subtotal: totals.subtotal,
    discount: totals.discount,
    discountType,
    tax: totals.tax,
    total: totals.total,
    paymentMethod,
    createdAt: now
  };

  try {
    // Save all data to IndexedDB
    await Promise.all([
      dbService.put('orders', order),
      dbService.put('invoices', invoice),
      dbService.put('receipts', receipt)
    ]);

    // Link order to table if it's a dine-in order
    if (orderType === 'dine-in' && tableId) {
      await linkOrderToTable(orderId, tableId);
    }

    return { order, invoice, receipt };
  } catch (error) {
    console.error('Error saving checkout data:', error);
    throw new Error('Failed to save order data');
  }
}

// Get all orders
export async function getAllOrders(): Promise<Order[]> {
  await dbService.init();
  const orders = await dbService.getAll<Order>('orders');
  return orders.sort((a, b) => b.createdAt - a.createdAt); // Sort by newest first
}

// Get orders by status
export async function getOrdersByStatus(status: string): Promise<Order[]> {
  await dbService.init();
  return await dbService.getByIndex<Order>('orders', 'status', status);
}

// Update order status
export async function updateOrderStatus(orderId: string, status: Order['status']): Promise<void> {
  await dbService.init();

  const order = await dbService.get<Order>('orders', orderId);
  if (!order) {
    throw new Error('Order not found');
  }

  const updatedOrder = {
    ...order,
    status,
    updatedAt: Date.now()
  };

  await dbService.put('orders', updatedOrder);

  // Clear table if order is completed or cancelled and it's a dine-in order
  if ((status === 'completed' || status === 'cancelled') && order.tableId) {
    await clearTable(order.tableId.toString());
  }

  // Also update invoice status if it exists
  const invoice = await dbService.get<Invoice>('invoices', order.invoiceId);
  if (invoice) {
    const updatedInvoice = {
      ...invoice,
      status,
      updatedAt: Date.now()
    };
    await dbService.put('invoices', updatedInvoice);
  }
}

// Get invoice by ID
export async function getInvoiceById(invoiceId: string): Promise<Invoice | null> {
  await dbService.init();
  return await dbService.get<Invoice>('invoices', invoiceId) || null;
}

// Get receipt by ID
export async function getReceiptById(receiptId: string): Promise<Receipt | null> {
  await dbService.init();
  return await dbService.get<Receipt>('receipts', receiptId) || null;
}

// Search orders by customer name
export async function searchOrdersByCustomer(customerName: string): Promise<Order[]> {
  await dbService.init();
  const allOrders = await dbService.getAll<Order>('orders');
  return allOrders.filter(order => 
    order.customerInfo.name.toLowerCase().includes(customerName.toLowerCase())
  );
}

// Get order statistics
export async function getOrderStatistics(): Promise<{
  totalOrders: number;
  pendingOrders: number;
  completedOrders: number;
  totalRevenue: number;
  todayOrders: number;
  todayRevenue: number;
}> {
  await dbService.init();
  const orders = await dbService.getAll<Order>('orders');
  
  const today = new Date();
  const todayStr = today.toLocaleDateString();
  
  const stats = {
    totalOrders: orders.length,
    pendingOrders: orders.filter(o => o.status === 'pending').length,
    completedOrders: orders.filter(o => o.status === 'completed').length,
    totalRevenue: orders.reduce((sum, o) => sum + o.total, 0),
    todayOrders: orders.filter(o => o.date === todayStr).length,
    todayRevenue: orders.filter(o => o.date === todayStr).reduce((sum, o) => sum + o.total, 0)
  };
  
  return stats;
}
